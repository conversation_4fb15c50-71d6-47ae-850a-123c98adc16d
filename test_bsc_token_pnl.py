#!/usr/bin/env python3
"""
Test script for unrealized P/L functionality with a real BSC token.
This script tests the new functionality with BSC tokens.
"""

import os
import sys
import logging
from datetime import datetime
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from clients.morails_client import MoralisClient
from clients.coingecko_client import CoinGeckoClient
from processing_bsc import munge_transactions_bsc, munge_swaps_bsc
from analysis_bsc import calculate_token_metrics_bsc, analyze_tokens_from_swaps_bsc
from token_constants_bsc import EXCLUDED_TOKENS

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
    ],
)

def test_bsc_token_analysis():
    """Test the unrealized P/L functionality with a real BSC token."""
    print("=" * 70)
    print("Testing Unrealized P/L with Real BSC Token Data")
    print("=" * 70)
    
    # Load environment variables
    load_dotenv()
    
    # Use the BSC wallet address from .env (commented out line)
    bsc_wallet_address = "******************************************"
    moralis_api_key = os.getenv("MORALIS_API_KEY")
    coingecko_api_key = os.getenv("COINGECKO_API_KEY")
    
    if not all([moralis_api_key, coingecko_api_key]):
        print("❌ Missing required API keys in .env file")
        return False
    
    print(f"🔍 Analyzing BSC wallet: {bsc_wallet_address}")
    
    # Initialize clients
    moralis_client = MoralisClient(moralis_api_key)
    coingecko_client = CoinGeckoClient(coingecko_api_key)
    
    try:
        # Step 1: Fetch recent SWAP transactions
        print(f"\n📊 Fetching SWAP transactions for the last 2 months...")
        swaps = moralis_client.get_swaps(bsc_wallet_address, months=2)
        
        if not swaps:
            print("❌ No SWAP transactions found")
            return False
        
        print(f"✅ Found {len(swaps)} SWAP transactions")
        
        # Step 2: Extract token information
        print(f"\n🔄 Processing transactions...")
        token_swaps, token_id_mapping = munge_transactions_bsc(
            swaps, moralis_client, coingecko_client
        )
        
        if not token_swaps:
            print("❌ No token swaps extracted")
            return False
        
        print(f"✅ Extracted {len(token_swaps)} token swap events")
        print(f"📋 Found {len(token_id_mapping)} unique tokens")
        
        # Step 3: Get ATH data
        print(f"\n📈 Fetching ATH data...")
        token_ath_data = coingecko_client.get_tokens_ath(token_id_mapping)
        
        # Step 4: Process swaps
        munged_swaps_df = munge_swaps_bsc(token_swaps, EXCLUDED_TOKENS)
        
        # Step 5: Find a token with remaining balance for testing
        print(f"\n🔍 Looking for tokens with remaining balance...")
        
        # Get tokens that are not excluded
        excluded_tokens_lower = {token.lower() for token in EXCLUDED_TOKENS}
        available_tokens = [
            address for address in token_id_mapping.keys() 
            if address.lower() not in excluded_tokens_lower
        ]
        
        test_token_found = False
        
        for token_address in available_tokens[:5]:  # Test first 5 tokens
            # Filter swaps for this token
            token_swaps_df = munged_swaps_df[
                (munged_swaps_df["input_mint"].str.lower() == token_address.lower()) |
                (munged_swaps_df["output_mint"].str.lower() == token_address.lower())
            ]
            
            if token_swaps_df.empty:
                continue
            
            # Calculate basic metrics to check remaining balance
            buy_swaps = token_swaps_df[token_swaps_df["action"] == "buy"]
            sell_swaps = token_swaps_df[token_swaps_df["action"] == "sell"]
            
            total_bought = buy_swaps["amount"].sum() if not buy_swaps.empty else 0
            total_sold = sell_swaps["amount"].sum() if not sell_swaps.empty else 0
            remaining_balance = total_bought - total_sold
            
            if remaining_balance > 0:
                print(f"\n🎯 Testing with token: {token_address}")
                print(f"📊 Token ID: {token_id_mapping.get(token_address, 'Unknown')}")
                print(f"💰 Remaining balance: {remaining_balance:,.6f}")
                
                # Test the new functionality
                test_bsc_token_unrealized_pnl(
                    token_address, 
                    token_id_mapping.get(token_address),
                    token_swaps_df,
                    token_ath_data.get(token_address, {}),
                    coingecko_client
                )
                
                test_token_found = True
                break
        
        if not test_token_found:
            print("⚠️  No tokens with remaining balance found in the first 5 tokens")
            print("📋 Available tokens:")
            for i, token_address in enumerate(available_tokens[:10]):
                token_id = token_id_mapping.get(token_address, 'Unknown')
                print(f"  {i+1}. {token_address[:8]}... (ID: {token_id})")
        
        return test_token_found
        
    except Exception as e:
        print(f"❌ Error during analysis: {str(e)}")
        logging.exception("Full error details:")
        return False

def test_bsc_token_unrealized_pnl(token_address, token_id, token_swaps_df, ath_data, coingecko_client):
    """Test unrealized P/L calculation for a specific BSC token."""
    print(f"\n{'='*50}")
    print(f"Testing BSC Token Unrealized P/L")
    print(f"{'='*50}")
    
    try:
        # Calculate metrics with the new functionality
        print(f"🧮 Calculating BSC token metrics with unrealized P/L...")
        
        token_metadata = calculate_token_metrics_bsc(
            token_swaps_df, token_id, token_address, ath_data, coingecko_client
        )
        
        # Display results
        print(f"\n📊 BSC Token Analysis Results:")
        print(f"  Token Address: {token_metadata['token_address']}")
        print(f"  Token ID: {token_metadata['token_id']}")
        print(f"  ATH Price: ${token_metadata.get('ath_price', 'N/A')}")
        print(f"  ATH Date: {token_metadata.get('ath_date', 'N/A')}")
        
        print(f"\n💰 Trading Summary:")
        print(f"  Total Bought: {token_metadata['total_bought']:,.6f}")
        print(f"  Total Sold: {token_metadata['total_sold']:,.6f}")
        print(f"  Remaining Balance: {token_metadata['remaining_balance']:,.6f}")
        print(f"  Average Buy Price: ${token_metadata['average_buy_price']:,.6f}")
        print(f"  Average Sell Price: ${token_metadata['average_sell_price']:,.6f}")
        
        print(f"\n📈 Realized P/L:")
        print(f"  Realized Profit/Loss: ${token_metadata['realized_profit_loss']:,.2f}")
        print(f"  Percentage Return: {token_metadata['percentage_return']:,.1f}%")
        
        print(f"\n🔮 Unrealized P/L (NEW FUNCTIONALITY):")
        print(f"  Current Unrealized P/L: ${token_metadata['unrealized_pnl_current']:,.2f}")
        print(f"  30-day High P/L: ${token_metadata['unrealized_pnl_30d_high']:,.2f}")
        print(f"  30-day Low P/L: ${token_metadata['unrealized_pnl_30d_low']:,.2f}")
        print(f"  30-day Average P/L: ${token_metadata['unrealized_pnl_30d_avg']:,.2f}")
        
        # Calculate percentage returns for unrealized P/L
        if token_metadata['remaining_balance'] > 0 and token_metadata['average_buy_price'] > 0:
            cost_basis = token_metadata['remaining_balance'] * token_metadata['average_buy_price']
            
            current_pct = (token_metadata['unrealized_pnl_current'] / cost_basis) * 100
            high_pct = (token_metadata['unrealized_pnl_30d_high'] / cost_basis) * 100
            low_pct = (token_metadata['unrealized_pnl_30d_low'] / cost_basis) * 100
            avg_pct = (token_metadata['unrealized_pnl_30d_avg'] / cost_basis) * 100
            
            print(f"\n📊 Unrealized P/L Percentages:")
            print(f"  Current: {current_pct:+.1f}%")
            print(f"  30-day High: {high_pct:+.1f}%")
            print(f"  30-day Low: {low_pct:+.1f}%")
            print(f"  30-day Average: {avg_pct:+.1f}%")
        
        print(f"\n🎯 Test Results:")
        if token_metadata['unrealized_pnl_current'] != 0:
            print(f"  ✅ BSC Unrealized P/L calculation working!")
            print(f"  ✅ 30-day price history successfully integrated!")
        else:
            print(f"  ⚠️  Unrealized P/L is zero (may be due to no price data)")
        
        # Show transaction count
        print(f"\n📋 Transaction Details:")
        print(f"  Total Transactions: {len(token_metadata['transactions'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error calculating BSC unrealized P/L: {str(e)}")
        logging.exception("Full error details:")
        return False

def main():
    """Main test function."""
    print("🚀 Starting BSC Token Unrealized P/L Test")
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    success = test_bsc_token_analysis()
    
    print(f"\n{'='*70}")
    if success:
        print("🎉 BSC Test completed successfully!")
        print("✅ New BSC unrealized P/L functionality is working!")
    else:
        print("❌ BSC Test failed or no suitable tokens found")
        print("💡 Try running the BSC wallet analysis to see available tokens")
    print(f"{'='*70}")

if __name__ == "__main__":
    main()
