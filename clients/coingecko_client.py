import time
import requests
import logging
from datetime import datetime, timedelta
from functools import lru_cache
from utils.utils import file_cache

class CoinGeckoClient:
    """Client for interacting with CoinGecko API to fetch token price data."""
    
    def __init__(self, demo_api_key):
        self.base_url = "https://api.coingecko.com/api/v3"
        self.headers = {"x-cg-demo-api-key": demo_api_key}
    
    @file_cache(ttl_hours=240)
    @lru_cache(maxsize=1000)
    def get_solana_price(self, date=None):
        """
        Get Solana price at a specific date.
        Uses both file caching and LRU caching to reduce API calls.
        
        Args:
            date (str): Date in format 'dd-mm-yyyy'. If None, current date is used.
            
        Returns:
            float: Solana price in USD at the given date
        """
        if date is None:
            date = datetime.now().strftime('%d-%m-%Y')
            
        try:
            # Get historical data for Solana
            url = f"{self.base_url}/coins/solana/history"
            params = {
                "date": date,
                "localization": "false"
            }
            
            response = requests.get(url, headers=self.headers, params=params)
            
            # Handle rate limit
            if response.status_code == 429:
                logging.warning("Rate limit hit, waiting 60 seconds")
                time.sleep(60)
                response = requests.get(url, headers=self.headers, params=params)
            
            if response.status_code == 200:
                data = response.json()
                price = data.get("market_data", {}).get("current_price", {}).get("usd", None)
                return price
            else:
                logging.error(f"Error fetching Solana price: {response.text}")
                return None
        except Exception as e:
            logging.error(f"Error in get_solana_price: {str(e)}")
            return None
    
    def create_token_id_mapping(self, token_mints):
        """
        Create a mapping of token mint addresses to CoinGecko token IDs.
        
        Args:
            token_mints (set): Set of token mint addresses
            
        Returns:
            dict: Mapping of mint addresses to token IDs
        """
        # Initialize token mapping
        token_id_mapping = {}
        
        # Create mapping of mint addresses to token IDs
        for mint in token_mints:
            # Get token ID from CoinGecko API
            token_id = self._get_token_id_by_address(mint)
            if token_id:
                token_id_mapping[mint] = token_id
                logging.info(f"Mapped token {mint} to CoinGecko ID: {token_id}")
            else:
                token_id_mapping[mint] = mint  # Keep original mint if no CoinGecko ID found
                logging.warning(f"No CoinGecko ID found for token {mint}, using mint address")
        
        return token_id_mapping
    
    def get_tokens_ath(self, token_id_mapping):
        """
        Get ATH data for tokens in the token ID mapping.

        Args:
            token_id_mapping (dict): Mapping of mint addresses to token IDs

        Returns:
            dict: Dictionary of ATH data for each token
        """
        token_ath_data = {}

        for mint, token_id in token_id_mapping.items():
            try:
                if token_id and token_id != mint:  # Only if we found a valid CoinGecko ID
                    # Get ATH data
                    ath_data = self._get_token_ath(token_id)

                    if ath_data:
                        token_ath_data[mint] = ath_data
                        logging.info(f"Got ATH data for token {mint} ({token_id}): ${ath_data['ath_price']:.2f} on {ath_data['ath_date']}")
            except Exception as e:
                logging.error(f"Error getting ATH data for token {mint}: {str(e)}")

        return token_ath_data

    def get_tokens_genesis_dates(self, token_id_mapping):
        """
        Get genesis dates for tokens in the token ID mapping.

        Args:
            token_id_mapping (dict): Mapping of mint addresses to token IDs

        Returns:
            dict: Dictionary of genesis dates for each token (mint address -> genesis date)
        """
        token_genesis_data = {}

        for mint, token_id in token_id_mapping.items():
            try:
                if token_id and token_id != mint:  # Only if we found a valid CoinGecko ID
                    # Get genesis date
                    genesis_date = self._get_token_genesis_date(token_id)

                    if genesis_date:
                        token_genesis_data[mint] = genesis_date
                        logging.info(f"Got genesis date for token {mint} ({token_id}): {genesis_date}")
                    else:
                        logging.warning(f"No genesis date found for token {mint} ({token_id})")
            except Exception as e:
                logging.error(f"Error getting genesis date for token {mint}: {str(e)}")

        return token_genesis_data
    
    @file_cache(ttl_hours=240)
    @lru_cache(maxsize=1000)
    def _get_token_id_by_address(self, token_address, platform="solana"):
        """
        Get CoinGecko token ID by contract address.
        Uses both file caching and LRU caching to reduce API calls.
        
        Args:
            token_address (str): Token contract address
            platform (str): Blockchain platform
            
        Returns:
            str: CoinGecko token ID or None if not found
        """
        logging.info(f"Looking up CoinGecko ID for token: {token_address}")
        
        url = f"{self.base_url}/coins/{platform}/contract/{token_address}"
        
        try:
            response = requests.get(url, headers=self.headers)
            if response.status_code == 200:
                data = response.json()
                return data.get("id")
            else:
                logging.warning(f"Error fetching token ID: {response.text}")
                return None
        except Exception as e:
            logging.error(f"Exception while fetching token ID: {str(e)}")
            return None
    
    @file_cache(ttl_hours=240)
    @lru_cache(maxsize=1000)
    def _get_token_data(self, token_id):
        """
        Fetch raw token data from CoinGecko API for a given token ID.
        Uses LRU caching to reduce API calls for the raw data.

        Args:
            token_id (str): CoinGecko token ID.

        Returns:
            dict: Raw JSON response data from the API, or None on error.
        """
        logging.info(f"Fetching raw data for token: {token_id}")
        url = f"{self.base_url}/coins/{token_id}"
        params = {"localization": "false", "tickers": "false", "market_data": "true",
                  "community_data": "false", "developer_data": "false"}

        try:
            response = requests.get(url, headers=self.headers, params=params)

            # Rate limit handling
            if response.status_code == 429:
                logging.warning("Rate limit hit, waiting 60 seconds")
                time.sleep(60)
                response = requests.get(url, headers=self.headers, params=params)

            if response.status_code == 200:
                return response.json()
            else:
                logging.warning(f"Error fetching raw token data for {token_id}: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            logging.error(f"Exception while fetching raw token data for {token_id}: {str(e)}")
            return None

    @lru_cache(maxsize=1000)
    def _get_token_ath(self, token_id):
        """
        Get the all-time high (ATH) price for a token using cached raw data.
        Uses file caching for the processed ATH data.

        Args:
            token_id (str): CoinGecko token ID

        Returns:
            dict: ATH price information or None if not found/error.
        """
        logging.info(f"Processing ATH for token: {token_id}")

        data = self._get_token_data(token_id) 

        if data:
            try:
                # Assume the structure exists and access directly
                ath = data["market_data"]["ath"].get("usd")
                ath_date_str = data["market_data"]["ath_date"].get("usd")
                ath_change_percentage = data["market_data"]["ath_change_percentage"].get("usd")

                # Check if the values themselves are None
                if ath is not None and ath_date_str is not None and ath_change_percentage is not None:
                    ath_data = {
                        "ath_price": ath,
                        "ath_date": datetime.strptime(ath_date_str.split("T")[0], "%Y-%m-%d").strftime("%Y-%m-%d"),
                        "ath_change_percentage": ath_change_percentage
                    }
                    return ath_data
                else:
                    logging.warning(f"Missing or None USD values in ATH data for {token_id}")
                    return None

            except KeyError as e:
                logging.warning(f"ATH data structure missing expected key '{e}' for {token_id}")
                return None
            except Exception as e:
                 logging.error(f"Exception processing ATH data for {token_id}: {str(e)}")
                 return None
        else:
            # Error logged in _get_token_data
            return None

    @lru_cache(maxsize=1000)
    def _get_token_genesis_date(self, token_id):
        """
        Get the genesis date for a token using cached raw data.

        Args:
            token_id (str): CoinGecko token ID

        Returns:
            str: Genesis date in 'YYYY-MM-DD' format or None if not found/error.
        """
        logging.info(f"Processing genesis date for token: {token_id}")
        
        data = self._get_token_data(token_id)

        try:
            genesis_date_str = data.get("genesis_date") 
            # Attempt to parse the date. Raises AttributeError if data is None.
            # Raises TypeError if genesis_date_str is None. Raises ValueError if format is wrong.
            return datetime.strptime(genesis_date_str, "%Y-%m-%d").strftime("%Y-%m-%d")
        except AttributeError: 
            # data was None, error already logged by _get_token_data
            return None
        except (KeyError, TypeError, ValueError):
            # Handles missing key, None value, or bad date format
            logging.warning(f"Could not extract valid genesis date for token {token_id}")
            return None
        except Exception as e:
            logging.error(f"Unexpected exception processing genesis date for {token_id}: {str(e)}")
            return None
    
    @lru_cache(maxsize=1000)
    def get_token_market_chart(self, token_id, days=1, interval="daily"):
        """
        Get historical market data (price, market cap, volume) for a token.
        Uses LRU caching to reduce API calls.

        Args:
            token_id (str): CoinGecko token ID.
            days (int): Data up to number of days ago (e.g., 1, 7, 30, max).
            interval (str): Data interval (can be 'daily').

        Returns:
            dict: Dictionary containing 'prices', 'market_caps', 'total_volumes' or None on error.
            Example format:
            {
              'prices': [[timestamp, price], ...],
              'market_caps': [[timestamp, market_cap], ...],
              'total_volumes': [[timestamp, volume], ...]
            }
        """
        logging.info(f"Fetching market chart for token ID: {token_id} for {days} days")

        url = f"{self.base_url}/coins/{token_id}/market_chart"
        params = {
            "vs_currency": "usd",
            "days": str(days),
            "interval": interval
        }

        try:
            response = requests.get(url, headers=self.headers, params=params)

            # Rate limit handling
            if response.status_code == 429:
                logging.warning("Rate limit hit, waiting 60 seconds")
                time.sleep(60)
                response = requests.get(url, headers=self.headers, params=params)

            if response.status_code == 200:
                data = response.json()
                # Ensure expected keys are present
                if all(key in data for key in ['prices', 'market_caps', 'total_volumes']):
                    logging.info(f"Successfully fetched market chart for {token_id}")
                    return data
                else:
                    logging.warning(f"Market chart data missing keys for {token_id}: {data}")
                    return None
            else:
                logging.error(f"Error fetching market chart for {token_id}: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            logging.error(f"Exception while fetching market chart for {token_id}: {str(e)}")
            return None

    @file_cache(ttl_hours=24)
    @lru_cache(maxsize=1000)
    def get_coin_price_history_30_days(self, token_id):
        """
        Get historical coin prices for the past 30 days using the /coins/{id}/history endpoint.
        Uses both file caching and LRU caching to reduce API calls.

        Args:
            token_id (str): CoinGecko token ID

        Returns:
            dict: Dictionary with dates as keys (YYYY-MM-DD format) and prices as values,
                  or None if error occurs. Example: {'2024-01-01': 45.67, '2024-01-02': 46.12, ...}
        """
        logging.info(f"Fetching 30-day price history for token: {token_id}")

        price_history = {}
        current_date = datetime.now()

        # Fetch prices for the past 30 days
        for days_ago in range(30):
            target_date = current_date - timedelta(days=days_ago)
            date_str = target_date.strftime('%d-%m-%Y')  # Format required by CoinGecko API
            date_key = target_date.strftime('%Y-%m-%d')  # Format for our return dictionary

            try:
                url = f"{self.base_url}/coins/{token_id}/history"
                params = {
                    "date": date_str,
                    "localization": "false"
                }

                response = requests.get(url, headers=self.headers, params=params)

                # Handle rate limit
                if response.status_code == 429:
                    logging.warning("Rate limit hit, waiting 60 seconds")
                    time.sleep(60)
                    response = requests.get(url, headers=self.headers, params=params)

                if response.status_code == 200:
                    data = response.json()
                    price = data.get("market_data", {}).get("current_price", {}).get("usd", None)
                    if price is not None:
                        price_history[date_key] = price
                        logging.debug(f"Got price for {token_id} on {date_key}: ${price}")
                    else:
                        logging.warning(f"No USD price found for {token_id} on {date_str}")
                else:
                    logging.warning(f"Error fetching price for {token_id} on {date_str}: {response.status_code} - {response.text}")

                # Add small delay to avoid hitting rate limits
                time.sleep(0.1)

            except Exception as e:
                logging.error(f"Exception while fetching price for {token_id} on {date_str}: {str(e)}")
                continue

        if price_history:
            logging.info(f"Successfully fetched {len(price_history)} days of price history for {token_id}")
            return price_history
        else:
            logging.error(f"Failed to fetch any price history for {token_id}")
            return None
    