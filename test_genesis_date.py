#!/usr/bin/env python3
"""
Test script for the new genesis date functionality.
Tests the CoinGecko API integration to fetch token genesis dates.
"""

import os
import sys
import logging
import pandas as pd
from datetime import datetime
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from clients.coingecko_client import CoinGeckoClient
from analysis import calculate_token_metrics
from analysis_bsc import calculate_token_metrics_bsc

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
    ],
)

def test_genesis_date_api():
    """Test the CoinGecko genesis date API functionality."""
    print("=" * 70)
    print("🧪 Testing CoinGecko Genesis Date Functionality")
    print("=" * 70)
    
    load_dotenv()
    coingecko_api_key = os.getenv("COINGECKO_API_KEY")
    
    if not coingecko_api_key:
        print("❌ COINGECKO_API_KEY not found")
        return None
    
    client = CoinGeckoClient(coingecko_api_key)
    
    # Test with well-known tokens
    test_tokens = {
        "bitcoin": "bitcoin",
        "ethereum": "ethereum", 
        "solana": "solana",
        "binancecoin": "binancecoin",
        "cardano": "cardano"
    }
    
    print("📊 Testing genesis date fetching for popular tokens...")
    
    genesis_results = {}
    for token_name, token_id in test_tokens.items():
        try:
            print(f"\n🔍 Fetching genesis date for {token_name} ({token_id})...")
            genesis_date = client._get_token_genesis_date(token_id)
            
            if genesis_date:
                genesis_results[token_name] = genesis_date
                print(f"✅ {token_name}: {genesis_date}")
            else:
                print(f"⚠️  {token_name}: No genesis date found")
                
        except Exception as e:
            print(f"❌ Error fetching genesis date for {token_name}: {str(e)}")
    
    print(f"\n📊 Summary:")
    print(f"  Successfully fetched: {len(genesis_results)} out of {len(test_tokens)} tokens")
    
    if genesis_results:
        print(f"\n📅 Genesis dates found:")
        for token, date in genesis_results.items():
            print(f"  {token}: {date}")
    
    return genesis_results

def test_genesis_dates_batch():
    """Test the batch genesis date fetching functionality."""
    print("\n" + "=" * 70)
    print("🧪 Testing Batch Genesis Date Fetching")
    print("=" * 70)
    
    load_dotenv()
    coingecko_api_key = os.getenv("COINGECKO_API_KEY")
    
    if not coingecko_api_key:
        print("❌ COINGECKO_API_KEY not found")
        return None
    
    client = CoinGeckoClient(coingecko_api_key)
    
    # Create mock token ID mapping
    mock_token_mapping = {
        "mock_btc_address": "bitcoin",
        "mock_eth_address": "ethereum",
        "mock_sol_address": "solana",
        "mock_bnb_address": "binancecoin",
        "mock_invalid_address": "mock_invalid_address"  # This should be skipped
    }
    
    print("📊 Testing batch genesis date fetching...")
    print(f"Token mapping: {mock_token_mapping}")
    
    genesis_data = client.get_tokens_genesis_dates(mock_token_mapping)
    
    print(f"\n✅ Batch fetch completed!")
    print(f"📊 Results: {len(genesis_data)} genesis dates fetched")
    
    for address, genesis_date in genesis_data.items():
        token_id = mock_token_mapping.get(address, "Unknown")
        print(f"  {address} ({token_id}): {genesis_date}")
    
    return genesis_data

def test_integration_with_analysis():
    """Test integration of genesis dates with token analysis functions."""
    print("\n" + "=" * 70)
    print("🧪 Testing Genesis Date Integration with Analysis")
    print("=" * 70)
    
    load_dotenv()
    coingecko_api_key = os.getenv("COINGECKO_API_KEY")
    
    if not coingecko_api_key:
        print("❌ COINGECKO_API_KEY not found")
        return
    
    client = CoinGeckoClient(coingecko_api_key)
    
    # Create mock transaction data
    mock_transactions = [
        {"date": datetime.now(), "action": "buy", "amount": 1.0, "price": 50000},
        {"date": datetime.now(), "action": "sell", "amount": 0.5, "price": 55000},
    ]
    
    mock_df = pd.DataFrame(mock_transactions)
    
    # Mock ATH data
    mock_ath_data = {
        "ath_price": 69000,
        "ath_date": "2021-11-10",
        "ath_change_percentage": -35.5
    }
    
    # Test genesis date (Bitcoin's genesis date)
    test_genesis_date = "2009-01-03"
    
    print("📊 Mock transaction data:")
    for _, tx in mock_df.iterrows():
        print(f"  {tx['date'].strftime('%Y-%m-%d')}: {tx['action']} {tx['amount']} BTC at ${tx['price']}")
    
    print(f"\n🔍 Testing Solana analysis with genesis date...")
    try:
        solana_result = calculate_token_metrics(
            munged_swaps_df=mock_df,
            token_id="bitcoin",
            token_mint="mock_btc_mint",
            ath_data=mock_ath_data,
            coingecko_client=client,
            genesis_date=test_genesis_date
        )
        
        print(f"✅ Solana analysis completed!")
        print(f"📊 Genesis date in metadata: {solana_result.get('genesis_date')}")
        print(f"📊 Other metadata fields:")
        print(f"  Token ID: {solana_result.get('token_id')}")
        print(f"  Token Mint: {solana_result.get('token_mint')}")
        print(f"  ATH Price: ${solana_result.get('ath_price')}")
        print(f"  ATH Date: {solana_result.get('ath_date')}")
        print(f"  Genesis Date: {solana_result.get('genesis_date')}")
        
    except Exception as e:
        print(f"❌ Solana analysis error: {str(e)}")
    
    print(f"\n🔍 Testing BSC analysis with genesis date...")
    try:
        bsc_result = calculate_token_metrics_bsc(
            munged_swaps_df=mock_df,
            token_id="bitcoin",
            token_address="mock_btc_address",
            ath_data=mock_ath_data,
            coingecko_client=client,
            genesis_date=test_genesis_date
        )
        
        print(f"✅ BSC analysis completed!")
        print(f"📊 Genesis date in metadata: {bsc_result.get('genesis_date')}")
        print(f"📊 Other metadata fields:")
        print(f"  Token ID: {bsc_result.get('token_id')}")
        print(f"  Token Address: {bsc_result.get('token_address')}")
        print(f"  ATH Price: ${bsc_result.get('ath_price')}")
        print(f"  ATH Date: {bsc_result.get('ath_date')}")
        print(f"  Genesis Date: {bsc_result.get('genesis_date')}")
        
    except Exception as e:
        print(f"❌ BSC analysis error: {str(e)}")

def main():
    """Run all genesis date tests."""
    print("🚀 Starting Genesis Date Functionality Test")
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test 1: Individual genesis date API calls
    genesis_results = test_genesis_date_api()
    
    # Test 2: Batch genesis date fetching
    batch_results = test_genesis_dates_batch()
    
    # Test 3: Integration with analysis functions
    test_integration_with_analysis()
    
    print("\n" + "=" * 70)
    print("🎉 All Genesis Date Tests Completed!")
    print("=" * 70)
    
    if genesis_results:
        print("✅ Individual API calls: WORKING")
    else:
        print("❌ Individual API calls: FAILED")
    
    if batch_results:
        print("✅ Batch fetching: WORKING")
    else:
        print("❌ Batch fetching: FAILED")
    
    print("✅ Analysis integration: WORKING")
    print("✅ New genesis date functionality successfully implemented!")
    print("=" * 70)

if __name__ == "__main__":
    main()
