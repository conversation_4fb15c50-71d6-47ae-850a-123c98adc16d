#!/usr/bin/env python3
"""
Test script for unrealized P/L functionality with a real Solana token.
This script fetches actual transaction data for a single token and tests the new functionality.
"""

import os
import sys
import logging
import pandas as pd
from datetime import datetime
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from clients.helius_client import HeliusClient
from clients.coingecko_client import CoinGeckoClient
from processing import munge_transactions, munge_swaps
from analysis import calculate_token_metrics, analyze_tokens_from_swaps
from token_constants import EXCLUDED_TOKENS

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
    ],
)

def test_single_token_analysis():
    """Test the unrealized P/L functionality with a real Solana token."""
    print("=" * 70)
    print("Testing Unrealized P/L with Real Solana Token Data")
    print("=" * 70)
    
    # Load environment variables
    load_dotenv()
    
    wallet_address = os.getenv("WALLET_ADDRESS")
    helius_api_key = os.getenv("HELIUS_API_KEY")
    coingecko_api_key = os.getenv("COINGECKO_API_KEY")

    print(f"🔍 DEBUG: WALLET_ADDRESS from env: {wallet_address}")
    print(f"🔍 DEBUG: All env vars: HELIUS_API_KEY={helius_api_key is not None}, COINGECKO_API_KEY={coingecko_api_key is not None}")

    # Force use the correct Solana wallet address if we detect a BSC address
    if wallet_address and wallet_address.startswith("0x"):
        print("⚠️  Detected BSC wallet address, using Solana wallet instead")
        wallet_address = "EuA4JAD4exsfqiaLwTuTZwECQbBbjxoccUkNmPHveCF5"

    if not all([wallet_address, helius_api_key, coingecko_api_key]):
        print("❌ Missing required API keys or wallet address in .env file")
        return False

    print(f"🔍 Analyzing wallet: {wallet_address}")
    
    # Initialize clients
    helius_client = HeliusClient(helius_api_key)
    coingecko_client = CoinGeckoClient(coingecko_api_key)
    
    try:
        # Step 1: Fetch recent SWAP transactions (last 2 months for better chance of finding tokens)
        print(f"\n📊 Fetching SWAP transactions for the last 2 months...")
        transactions = helius_client.get_transactions(
            wallet_address, months=2, transaction_type="SWAP"
        )
        
        if not transactions:
            print("❌ No SWAP transactions found")
            return False
        
        print(f"✅ Found {len(transactions)} SWAP transactions")
        
        # Step 2: Extract token information
        print(f"\n🔄 Processing transactions...")
        token_swaps, token_id_mapping = munge_transactions(
            transactions, helius_client, coingecko_client
        )
        
        if not token_swaps:
            print("❌ No token swaps extracted")
            return False
        
        print(f"✅ Extracted {len(token_swaps)} token swap events")
        print(f"📋 Found {len(token_id_mapping)} unique tokens")
        
        # Step 3: Get ATH data
        print(f"\n📈 Fetching ATH data...")
        token_ath_data = coingecko_client.get_tokens_ath(token_id_mapping)
        
        # Step 4: Process swaps
        munged_swaps_df = munge_swaps(token_swaps, EXCLUDED_TOKENS)
        
        # Step 5: Find a token with remaining balance for testing
        print(f"\n🔍 Looking for tokens with remaining balance...")
        
        # Get tokens that are not excluded
        available_tokens = [
            mint for mint in token_id_mapping.keys() 
            if mint not in EXCLUDED_TOKENS
        ]
        
        test_token_found = False
        
        for token_mint in available_tokens[:5]:  # Test first 5 tokens
            # Filter swaps for this token
            token_swaps_df = munged_swaps_df[
                (munged_swaps_df["input_mint"] == token_mint) |
                (munged_swaps_df["output_mint"] == token_mint)
            ]
            
            if token_swaps_df.empty:
                continue
            
            # Calculate basic metrics to check remaining balance
            buy_swaps = token_swaps_df[token_swaps_df["action"] == "buy"]
            sell_swaps = token_swaps_df[token_swaps_df["action"] == "sell"]
            
            total_bought = buy_swaps["amount"].sum() if not buy_swaps.empty else 0
            total_sold = sell_swaps["amount"].sum() if not sell_swaps.empty else 0
            remaining_balance = total_bought - total_sold
            
            if remaining_balance > 0:
                print(f"\n🎯 Testing with token: {token_mint}")
                print(f"📊 Token ID: {token_id_mapping.get(token_mint, 'Unknown')}")
                print(f"💰 Remaining balance: {remaining_balance:,.6f}")
                
                # Test the new functionality
                test_token_unrealized_pnl(
                    token_mint, 
                    token_id_mapping.get(token_mint),
                    token_swaps_df,
                    token_ath_data.get(token_mint, {}),
                    coingecko_client
                )
                
                test_token_found = True
                break
        
        if not test_token_found:
            print("⚠️  No tokens with remaining balance found in the first 5 tokens")
            print("📋 Available tokens:")
            for i, token_mint in enumerate(available_tokens[:10]):
                token_id = token_id_mapping.get(token_mint, 'Unknown')
                print(f"  {i+1}. {token_mint[:8]}... (ID: {token_id})")
        
        return test_token_found
        
    except Exception as e:
        print(f"❌ Error during analysis: {str(e)}")
        logging.exception("Full error details:")
        return False

def test_token_unrealized_pnl(token_mint, token_id, token_swaps_df, ath_data, coingecko_client):
    """Test unrealized P/L calculation for a specific token."""
    print(f"\n{'='*50}")
    print(f"Testing Unrealized P/L for Token")
    print(f"{'='*50}")
    
    try:
        # Calculate metrics with the new functionality
        print(f"🧮 Calculating token metrics with unrealized P/L...")
        
        token_metadata = calculate_token_metrics(
            token_swaps_df, token_id, token_mint, ath_data, coingecko_client
        )
        
        # Display results
        print(f"\n📊 Token Analysis Results:")
        print(f"  Token Mint: {token_metadata['token_mint']}")
        print(f"  Token ID: {token_metadata['token_id']}")
        print(f"  ATH Price: ${token_metadata.get('ath_price', 'N/A')}")
        print(f"  ATH Date: {token_metadata.get('ath_date', 'N/A')}")
        
        print(f"\n💰 Trading Summary:")
        print(f"  Total Bought: {token_metadata['total_bought']:,.6f}")
        print(f"  Total Sold: {token_metadata['total_sold']:,.6f}")
        print(f"  Remaining Balance: {token_metadata['remaining_balance']:,.6f}")
        print(f"  Average Buy Price: ${token_metadata['average_buy_price']:,.6f}")
        print(f"  Average Sell Price: ${token_metadata['average_sell_price']:,.6f}")
        
        print(f"\n📈 Realized P/L:")
        print(f"  Realized Profit/Loss: ${token_metadata['realized_profit_loss']:,.2f}")
        print(f"  Percentage Return: {token_metadata['percentage_return']:,.1f}%")
        
        print(f"\n🔮 Unrealized P/L (NEW FUNCTIONALITY):")
        print(f"  Current Unrealized P/L: ${token_metadata['unrealized_pnl_current']:,.2f}")
        print(f"  30-day High P/L: ${token_metadata['unrealized_pnl_30d_high']:,.2f}")
        print(f"  30-day Low P/L: ${token_metadata['unrealized_pnl_30d_low']:,.2f}")
        print(f"  30-day Average P/L: ${token_metadata['unrealized_pnl_30d_avg']:,.2f}")
        
        # Calculate percentage returns for unrealized P/L
        if token_metadata['remaining_balance'] > 0 and token_metadata['average_buy_price'] > 0:
            cost_basis = token_metadata['remaining_balance'] * token_metadata['average_buy_price']
            
            current_pct = (token_metadata['unrealized_pnl_current'] / cost_basis) * 100
            high_pct = (token_metadata['unrealized_pnl_30d_high'] / cost_basis) * 100
            low_pct = (token_metadata['unrealized_pnl_30d_low'] / cost_basis) * 100
            avg_pct = (token_metadata['unrealized_pnl_30d_avg'] / cost_basis) * 100
            
            print(f"\n📊 Unrealized P/L Percentages:")
            print(f"  Current: {current_pct:+.1f}%")
            print(f"  30-day High: {high_pct:+.1f}%")
            print(f"  30-day Low: {low_pct:+.1f}%")
            print(f"  30-day Average: {avg_pct:+.1f}%")
        
        print(f"\n🎯 Test Results:")
        if token_metadata['unrealized_pnl_current'] != 0:
            print(f"  ✅ Unrealized P/L calculation working!")
            print(f"  ✅ 30-day price history successfully integrated!")
        else:
            print(f"  ⚠️  Unrealized P/L is zero (may be due to no price data)")
        
        # Show transaction count
        print(f"\n📋 Transaction Details:")
        print(f"  Total Transactions: {len(token_metadata['transactions'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error calculating unrealized P/L: {str(e)}")
        logging.exception("Full error details:")
        return False

def main():
    """Main test function."""
    print("🚀 Starting Solana Token Unrealized P/L Test")
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    success = test_single_token_analysis()
    
    print(f"\n{'='*70}")
    if success:
        print("🎉 Test completed successfully!")
        print("✅ New unrealized P/L functionality is working!")
    else:
        print("❌ Test failed or no suitable tokens found")
        print("💡 Try running the wallet analysis to see available tokens")
    print(f"{'='*70}")

if __name__ == "__main__":
    main()
