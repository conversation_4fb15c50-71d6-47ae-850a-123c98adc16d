#!/usr/bin/env python3
"""
Test script for the new unrealized P/L functionality.
Tests the 30-day price history fetching and unrealized P/L calculation for a single token.
"""

import os
import sys
import logging
import pandas as pd
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from clients.coingecko_client import CoinGeckoClient
from analysis import calculate_unrealized_pnl_30_days

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
    ],
)

def test_coingecko_price_history():
    """Test the new get_coin_price_history_30_days method."""
    print("=" * 60)
    print("Testing CoinGecko 30-day price history functionality")
    print("=" * 60)
    
    # Load environment variables
    load_dotenv()
    coingecko_api_key = os.getenv("COINGECKO_API_KEY")
    
    if not coingecko_api_key:
        print("❌ COINGECKO_API_KEY not found in .env file")
        return False
    
    # Initialize CoinGecko client
    client = CoinGeckoClient(coingecko_api_key)
    
    # Test with a well-known token (Bitcoin)
    test_token_id = "bitcoin"
    print(f"\n🔍 Testing with token: {test_token_id}")
    
    try:
        # Test the new method
        print(f"📊 Fetching 30-day price history for {test_token_id}...")
        price_history = client.get_coin_price_history_30_days(test_token_id)
        
        if price_history:
            print(f"✅ Successfully fetched price history!")
            print(f"📈 Got {len(price_history)} days of price data")
            
            # Show some sample data
            sorted_dates = sorted(price_history.keys())
            print(f"\n📅 Date range: {sorted_dates[0]} to {sorted_dates[-1]}")
            
            # Show first few and last few prices
            print(f"\n💰 Sample prices:")
            for i, date in enumerate(sorted_dates[:3]):
                print(f"  {date}: ${price_history[date]:,.2f}")
            if len(sorted_dates) > 6:
                print("  ...")
                for date in sorted_dates[-3:]:
                    print(f"  {date}: ${price_history[date]:,.2f}")
            
            # Calculate some basic stats
            prices = list(price_history.values())
            min_price = min(prices)
            max_price = max(prices)
            avg_price = sum(prices) / len(prices)
            
            print(f"\n📊 Price statistics over 30 days:")
            print(f"  Min: ${min_price:,.2f}")
            print(f"  Max: ${max_price:,.2f}")
            print(f"  Avg: ${avg_price:,.2f}")
            print(f"  Range: {((max_price - min_price) / min_price * 100):.1f}%")
            
            return price_history
        else:
            print(f"❌ Failed to fetch price history for {test_token_id}")
            return None
            
    except Exception as e:
        print(f"❌ Error testing price history: {str(e)}")
        return None

def test_unrealized_pnl_calculation(price_history):
    """Test the unrealized P/L calculation function."""
    print("\n" + "=" * 60)
    print("Testing Unrealized P/L Calculation")
    print("=" * 60)
    
    if not price_history:
        print("❌ No price history available for testing")
        return False
    
    # Create test scenarios
    test_scenarios = [
        {
            "name": "Profitable Position",
            "remaining_balance": 0.5,  # 0.5 BTC
            "avg_buy_price": 45000,    # Bought at $45,000
            "description": "Bought 0.5 BTC at $45,000"
        },
        {
            "name": "Loss Position", 
            "remaining_balance": 1.0,   # 1.0 BTC
            "avg_buy_price": 70000,     # Bought at $70,000
            "description": "Bought 1.0 BTC at $70,000"
        },
        {
            "name": "Small Position",
            "remaining_balance": 0.01,  # 0.01 BTC
            "avg_buy_price": 50000,     # Bought at $50,000
            "description": "Bought 0.01 BTC at $50,000"
        }
    ]
    
    # Get current price (most recent in history)
    sorted_dates = sorted(price_history.keys(), reverse=True)
    current_price = price_history[sorted_dates[0]]
    print(f"💰 Current price (most recent): ${current_price:,.2f}")
    
    for scenario in test_scenarios:
        print(f"\n🧪 Testing scenario: {scenario['name']}")
        print(f"📝 {scenario['description']}")
        
        try:
            # Calculate unrealized P/L
            pnl_metrics = calculate_unrealized_pnl_30_days(
                remaining_balance=scenario['remaining_balance'],
                avg_buy_price=scenario['avg_buy_price'],
                price_history=price_history,
                current_price=current_price
            )
            
            print(f"📊 Results:")
            print(f"  Current Unrealized P/L: ${pnl_metrics['unrealized_pnl_current']:,.2f}")
            print(f"  30-day High P/L: ${pnl_metrics['unrealized_pnl_30d_high']:,.2f}")
            print(f"  30-day Low P/L: ${pnl_metrics['unrealized_pnl_30d_low']:,.2f}")
            print(f"  30-day Avg P/L: ${pnl_metrics['unrealized_pnl_30d_avg']:,.2f}")
            
            # Calculate percentage returns
            cost_basis = scenario['remaining_balance'] * scenario['avg_buy_price']
            current_return_pct = (pnl_metrics['unrealized_pnl_current'] / cost_basis) * 100
            high_return_pct = (pnl_metrics['unrealized_pnl_30d_high'] / cost_basis) * 100
            low_return_pct = (pnl_metrics['unrealized_pnl_30d_low'] / cost_basis) * 100
            
            print(f"📈 Percentage Returns:")
            print(f"  Current: {current_return_pct:+.1f}%")
            print(f"  30-day High: {high_return_pct:+.1f}%")
            print(f"  30-day Low: {low_return_pct:+.1f}%")
            
        except Exception as e:
            print(f"❌ Error calculating P/L for {scenario['name']}: {str(e)}")
    
    return True

def test_edge_cases():
    """Test edge cases for the unrealized P/L calculation."""
    print("\n" + "=" * 60)
    print("Testing Edge Cases")
    print("=" * 60)
    
    edge_cases = [
        {
            "name": "Zero Balance",
            "remaining_balance": 0,
            "avg_buy_price": 50000,
            "price_history": {"2024-01-01": 55000},
            "current_price": 55000
        },
        {
            "name": "Zero Buy Price",
            "remaining_balance": 1.0,
            "avg_buy_price": 0,
            "price_history": {"2024-01-01": 55000},
            "current_price": 55000
        },
        {
            "name": "No Price History",
            "remaining_balance": 1.0,
            "avg_buy_price": 50000,
            "price_history": None,
            "current_price": 55000
        },
        {
            "name": "Empty Price History",
            "remaining_balance": 1.0,
            "avg_buy_price": 50000,
            "price_history": {},
            "current_price": 55000
        }
    ]
    
    for case in edge_cases:
        print(f"\n🧪 Testing edge case: {case['name']}")
        
        try:
            pnl_metrics = calculate_unrealized_pnl_30_days(
                remaining_balance=case['remaining_balance'],
                avg_buy_price=case['avg_buy_price'],
                price_history=case['price_history'],
                current_price=case['current_price']
            )
            
            print(f"✅ Result: {pnl_metrics}")
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")

def main():
    """Main test function."""
    print("🚀 Starting Unrealized P/L Functionality Test")
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test 1: CoinGecko price history fetching
    price_history = test_coingecko_price_history()
    
    # Test 2: Unrealized P/L calculation with real data
    if price_history:
        test_unrealized_pnl_calculation(price_history)
    
    # Test 3: Edge cases
    test_edge_cases()
    
    print("\n" + "=" * 60)
    print("🎉 Test completed!")
    print("=" * 60)

if __name__ == "__main__":
    main()
