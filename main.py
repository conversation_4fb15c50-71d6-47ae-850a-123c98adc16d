import os
import argparse
import logging
from dotenv import load_dotenv
from clients.helius_client import HeliusClient
from clients.coingecko_client import CoinGeckoClient
from processing import munge_transactions, munge_swaps_for_token, munge_swaps
from analysis import calculate_token_metrics, analyze_tokens_from_swaps
from utils.utils import ensure_output_dir, save_token_metadata
from token_constants import EXCLUDED_TOKENS

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("solana_token_analyzer.log"),
        logging.StreamHandler(),
    ],
)


def setup_args():
    """Set up command line arguments."""
    parser = argparse.ArgumentParser(
        description="Analyze Solana SPL token SWAP transactions"
    )
    parser.add_argument(
        "--wallet", type=str, help="Solana wallet address to analyze (overrides .env)"
    )
    parser.add_argument(
        "--months", type=int, default=6, help="Number of months to look back"
    )
    parser.add_argument(
        "--output", type=str, default="output", help="Output directory for results"
    )
    parser.add_argument(
        "--helius-key", type=str, help="Helius API key (overrides .env)"
    )
    parser.add_argument(
        "--coingecko-key", type=str, help="CoinGecko API key (overrides .env)"
    )
    parser.add_argument(
        "--openai-key", type=str, help="OpenAI API key (overrides .env)"
    )
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")

    return parser.parse_args()


def main():
    """Main function to analyze Solana SPL token transactions."""
    # Load environment variables
    load_dotenv()

    # Parse command line arguments
    args = setup_args()

    # Set logging level
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)

    # Get wallet address from args or .env
    wallet_address = args.wallet or os.getenv("WALLET_ADDRESS")
    if not wallet_address:
        logging.error(
            "Wallet address is required. Set it in .env file or provide with --wallet."
        )
        return

    # Get API keys from args or .env
    helius_api_key = os.getenv("HELIUS_API_KEY")
    coingecko_api_key = os.getenv("COINGECKO_API_KEY")
    # openai_api_key = os.getenv('OPENAI_API_KEY')

    if not helius_api_key:
        logging.error(
            "Helius API key is required. Set it in .env file or provide with --helius-key."
        )
        return

    if not coingecko_api_key:
        logging.error(
            "CoinGecko API key is required. Set it in .env file or provide with --coingecko-key."
        )
        return

    # if not openai_api_key:
    #     logging.error("OpenAI API key is required. Set it in .env file or provide with --openai-key.")
    #     return

    # Ensure output and cache directories exist
    ensure_output_dir(args.output)

    # Initialize clients
    helius_client = HeliusClient(helius_api_key)
    coingecko_client = CoinGeckoClient(coingecko_api_key)

    # Get token accounts owned by the wallet
    # Step 1: Fetch SWAP transactions
    logging.info(f"Analyzing SPL token SWAP transactions for wallet: {wallet_address}")
    logging.info(f"Looking back {args.months} months")

    transactions = helius_client.get_transactions(
        wallet_address, months=args.months, transaction_type="SWAP"
    )

    if not transactions:
        logging.warning(f"No SWAP transactions found for wallet {wallet_address}")
        return

    logging.info(f"Found {len(transactions)} SWAP transactions")

    # Step 2: Extract token information from SWAP transactions
    token_swaps, token_id_mapping = munge_transactions(
        transactions, helius_client, coingecko_client
    )

    if not token_swaps:
        logging.warning("No token swaps extracted from transactions")
        return

    logging.info(f"Extracted {len(token_swaps)} token swap events")

    # Get ATH data for each token using the new method from CoinGeckoClient
    token_ath_data = coingecko_client.get_tokens_ath(token_id_mapping)

    munged_swaps_df = munge_swaps(token_swaps, EXCLUDED_TOKENS)

    # Analyze tokens using the new function
    analyzed_tokens = analyze_tokens_from_swaps(
        token_id_mapping, munged_swaps_df, token_ath_data, coingecko_client
    )

    # Save token metadata to JSON files
    save_token_metadata(analyzed_tokens, args.output)

    # # Optional: Analyze tokens with OpenAI if API key is provided
    # if openai_api_key:
    #     token_metadata_dir = os.path.join(args.output, "token_metadata")
    #     for token_metadata in processed_tokens:
    #         token_id = token_metadata["token_id"]
    #         if token_id and token_id != token_metadata["token_mint"]:  # Only if we found a valid CoinGecko ID
    #             analyze_token_with_openai(token_id, token_metadata_dir, args.output, openai_api_key)

    logging.info(f"Analysis complete. Results saved to {args.output} directory")


if __name__ == "__main__":
    main()
